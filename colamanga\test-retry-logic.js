const { MangaContentDownloader } = require('./download-manga-content');

/**
 * 测试重试逻辑
 */
async function testRetryLogic() {
    console.log('🧪 测试重试逻辑...\n');
    
    const downloader = new MangaContentDownloader();
    
    try {
        // 初始化浏览器
        console.log('1️⃣ 初始化浏览器...');
        await downloader.init();
        console.log('✅ 浏览器初始化成功\n');
        
        // 测试单个章节下载（容易失败的章节）
        console.log('2️⃣ 测试章节下载重试逻辑...');
        
        // 你可以替换为实际遇到问题的漫画ID和章节
        const testMangaId = 'ap101511'; // 替换为实际的漫画ID
        const testMangaName = '测试漫画';
        const testChapter = 8; // 之前失败的章节
        
        console.log(`📖 测试下载: ${testMangaName} 第${testChapter}章 (带重试逻辑)`);
        
        const result = await downloader.downloadMangaContent(
            testMangaId, 
            testMangaName, 
            testChapter, 
            true // 跳过漫画信息获取，专注测试章节下载
        );
        
        if (result) {
            console.log('✅ 章节下载测试成功（重试逻辑生效）');
        } else {
            console.log('❌ 章节下载测试失败（即使有重试逻辑）');
        }
        
        console.log('\n3️⃣ 测试完成');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
    } finally {
        // 关闭浏览器
        console.log('\n4️⃣ 清理资源...');
        await downloader.close();
        console.log('✅ 测试完成，浏览器已关闭');
    }
}

/**
 * 测试多章节下载的稳定性
 */
async function testMultiChapterStability() {
    console.log('🧪 测试多章节下载稳定性...\n');
    
    const downloader = new MangaContentDownloader();
    
    try {
        await downloader.init();
        
        // 测试连续下载多个章节
        const testMangaId = 'ap101511'; // 替换为实际的漫画ID
        const testMangaName = '测试漫画';
        const startChapter = 1;
        const endChapter = 5;
        
        let successCount = 0;
        let failCount = 0;
        
        for (let chapter = startChapter; chapter <= endChapter; chapter++) {
            console.log(`\n📖 下载第 ${chapter} 章...`);
            
            try {
                const result = await downloader.downloadMangaContent(
                    testMangaId, 
                    testMangaName, 
                    chapter, 
                    chapter > 1 // 第一章获取漫画信息，后续章节跳过
                );
                
                if (result) {
                    successCount++;
                    console.log(`✅ 第 ${chapter} 章下载成功`);
                } else {
                    failCount++;
                    console.log(`❌ 第 ${chapter} 章下载失败`);
                }
            } catch (error) {
                failCount++;
                console.error(`❌ 第 ${chapter} 章下载异常: ${error.message}`);
            }
            
            // 章节间稍作休息
            if (chapter < endChapter) {
                console.log(`⏳ 休息 2 秒后继续下一章...`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        console.log(`\n📊 多章节下载统计:`);
        console.log(`   ✅ 成功: ${successCount}/${endChapter - startChapter + 1}`);
        console.log(`   ❌ 失败: ${failCount}/${endChapter - startChapter + 1}`);
        console.log(`   📈 成功率: ${(successCount / (endChapter - startChapter + 1) * 100).toFixed(1)}%`);
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    } finally {
        await downloader.close();
    }
}

// 根据命令行参数选择测试类型
const testType = process.argv[2] || 'retry';

if (testType === 'multi') {
    testMultiChapterStability().catch(console.error);
} else {
    testRetryLogic().catch(console.error);
}
