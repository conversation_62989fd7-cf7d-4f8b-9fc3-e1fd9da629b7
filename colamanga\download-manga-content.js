const { chromium } = require('playwright');
const fs = require('fs-extra');
const path = require('path');

/**
 * 简化的漫画内容下载器
 * 核心功能：
 * 1. 单浏览器实例，串行处理
 * 2. 核心流程：获取简介 -> 进入章节 -> 滚动页面 -> 等待blob加载 -> 下载图片
 * 3. 去除所有并发逻辑，专注核心功能
 */
class MangaContentDownloader {
    constructor(options = {}) {
        this.outputDir = 'E:\\manga';
        this.browser = null;
        this.page = null;
        this.context = null;
        
        // 统计信息
        this.stats = {
            totalMangasProcessed: 0,
            totalChaptersDownloaded: 0,
            totalImagesDownloaded: 0,
            totalErrors: 0
        };

        console.log(`🔧 漫画下载器初始化完成`);
    }

    /**
     * 初始化浏览器
     */
    async init() {
        console.log('🚀 启动浏览器...');
        
        this.context = await chromium.launchPersistentContext('', {
            headless: false,
            channel: 'chrome',
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=TranslateUI',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-back-forward-cache',
                '--disable-background-networking',
                '--disable-default-apps',
                '--disable-sync',
                '--disable-translate',
                '--mute-audio',
                '--no-first-run',
                '--disable-plugins'
            ],
            ignoreDefaultArgs: [
                '--enable-automation',
                '--disable-extensions',
                '--disable-component-extensions-with-background-pages'
            ]
        });

        this.page = await this.context.newPage();
        
        // 设置页面配置
        await this.page.setExtraHTTPHeaders({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        });
        
        this.page.setDefaultTimeout(60000);
        this.page.setDefaultNavigationTimeout(60000);
        await this.page.setViewportSize({ width: 1280, height: 720 });

        // 设置资源拦截
        await this.setupResourceInterception();
        
        // 设置blob图片捕获
        await this.setupBlobCapture();

        // 确保输出目录存在
        await fs.ensureDir(this.outputDir);
        
        console.log('✅ 浏览器初始化完成');
    }

    /**
     * 设置资源拦截以优化性能
     */
    async setupResourceInterception() {
        await this.page.route('**/*', (route) => {
            try {
                const request = route.request();
                const resourceType = request.resourceType();
                const url = request.url();

                // 拦截不必要的资源
                if (url.includes('google-analytics') ||
                    url.includes('googletagmanager') ||
                    url.includes('doubleclick.net') ||
                    url.includes('googlesyndication') ||
                    url.includes('facebook.com/tr')) {
                    route.abort();
                } else if (resourceType === 'font' && !url.includes('colamanga.com')) {
                    route.abort();
                } else {
                    route.continue();
                }
            } catch (error) {
                route.continue();
            }
        });
    }

    /**
     * 设置blob图片捕获
     */
    async setupBlobCapture() {
        await this.page.addInitScript(() => {
            const originalCreateObjectURL = URL.createObjectURL;
            URL.createObjectURL = function (object) {
                const blobUrl = originalCreateObjectURL.call(this, object);
                window.__blobUrls = window.__blobUrls || [];
                window.__blobUrls.push({
                    blobUrl: blobUrl,
                    size: object.size,
                    type: object.type,
                    timestamp: Date.now()
                });
                return blobUrl;
            };
        });
    }

    /**
     * 关闭浏览器
     */
    async close() {
        console.log('🔄 关闭浏览器...');
        
        if (this.context) {
            await this.context.close();
            console.log('✅ 浏览器已关闭');
        }
    }

    // ==================== 核心下载方法 ====================

    /**
     * 从漫画列表文件下载漫画
     */
    async downloadFromMangaList(mangaListFile, startIndex = 0, count = null, maxChapters = null) {
        console.log(`📚 开始从漫画列表下载: ${mangaListFile}`);
        
        // 读取漫画列表
        const mangaList = await fs.readJson(mangaListFile);
        console.log(`📖 漫画列表包含 ${mangaList.length} 个漫画`);
        
        // 应用范围限制
        const targetList = count ? mangaList.slice(startIndex, startIndex + count) : mangaList.slice(startIndex);
        console.log(`🎯 目标下载 ${targetList.length} 个漫画 (从索引 ${startIndex} 开始)`);
        
        // 串行下载漫画
        const results = [];
        for (let i = 0; i < targetList.length; i++) {
            const manga = targetList[i];
            console.log(`\n📖 [${i + 1}/${targetList.length}] 下载漫画: ${manga.name}`);
            
            try {
                const result = await this.downloadSingleManga(manga, maxChapters);
                results.push({ manga, result, index: i });
            } catch (error) {
                console.error(`❌ 下载失败: ${manga.name} - ${error.message}`);
                results.push({ manga, result: { success: false, error: error.message }, index: i });
            }
        }
        
        return results;
    }

    /**
     * 检查漫画是否已完成下载
     */
    async checkMangaCompletion(manga) {
        const mangaDir = path.join(this.outputDir, this.sanitizeFileName(manga.name));
        
        // 检查漫画目录是否存在
        if (!(await fs.pathExists(mangaDir))) {
            return false;
        }
        
        // 检查是否有漫画信息文件
        const infoFile = path.join(mangaDir, 'manga-info.json');
        if (!(await fs.pathExists(infoFile))) {
            return false;
        }
        
        // 简单检查：如果有多个章节目录，认为基本完成
        const entries = await fs.readdir(mangaDir);
        const chapterDirs = entries.filter(entry => entry.startsWith('第') && entry.includes('章'));
        
        // 如果有5个或以上章节，认为已完成（可根据需要调整）
        return chapterDirs.length >= 5;
    }

    /**
     * 下载单个漫画的所有章节
     */
    async downloadSingleManga(manga, maxChapters = null) {
        console.log(`📖 开始下载漫画: ${manga.name} (ID: ${manga.id})`);
        
        const startTime = Date.now();
        let totalChapters = 0;
        let successfulChapters = 0;
        let failedChapters = 0;
        
        try {
            // 检查是否已完成下载
            if (await this.checkMangaCompletion(manga)) {
                console.log(`✅ ${manga.name} 已完成下载，跳过`);
                return {
                    success: true,
                    totalChapters: manga.maxChapter || 0,
                    successfulChapters: manga.maxChapter || 0,
                    failedChapters: 0,
                    duration: Date.now() - startTime
                };
            }
            
            // 确定下载的最大章节数
            const maxChapterToDownload = maxChapters || manga.maxChapter || 999;
            console.log(`📚 计划下载章节 1-${maxChapterToDownload}`);
            
            // 串行下载章节
            let consecutiveFailures = 0;
            
            for (let chapter = 1; chapter <= maxChapterToDownload; chapter++) {
                try {
                    const skipMangaInfo = chapter > 1; // 只在第一章获取漫画信息
                    const result = await this.downloadMangaContent(manga.id, manga.name, chapter, skipMangaInfo);
                    
                    totalChapters++;
                    if (result) {
                        successfulChapters++;
                        consecutiveFailures = 0; // 重置连续失败计数
                        console.log(`✅ 章节 ${chapter} 下载成功`);
                    } else {
                        failedChapters++;
                        consecutiveFailures++;
                        console.log(`❌ 章节 ${chapter} 下载失败`);
                    }
                } catch (error) {
                    console.error(`❌ 章节 ${chapter} 下载异常: ${error.message}`);
                    totalChapters++;
                    failedChapters++;
                    consecutiveFailures++;
                }
                
                // 如果连续失败多章，可能是漫画结束了
                if (consecutiveFailures >= 3) {
                    console.log(`⚠️ 连续失败${consecutiveFailures}章，可能已到漫画结尾，停止下载`);
                    break;
                }
            }
            
            const duration = Date.now() - startTime;
            const success = successfulChapters > 0;
            
            console.log(`📊 漫画 ${manga.name} 下载完成:`);
            console.log(`   - 总章节: ${totalChapters}`);
            console.log(`   - 成功: ${successfulChapters}`);
            console.log(`   - 失败: ${failedChapters}`);
            console.log(`   - 耗时: ${(duration / 1000).toFixed(1)}秒`);
            
            return {
                success,
                totalChapters,
                successfulChapters,
                failedChapters,
                duration
            };
            
        } catch (error) {
            console.error(`❌ 下载漫画失败: ${manga.name} - ${error.message}`);
            return {
                success: false,
                error: error.message,
                totalChapters,
                successfulChapters,
                failedChapters,
                duration: Date.now() - startTime
            };
        }
    }

    // ==================== 章节下载核心方法 ====================

    /**
     * 下载单个章节的内容
     */
    async downloadMangaContent(mangaId, mangaName, chapter = 1, skipMangaInfo = false) {
        console.log(`📖 开始下载漫画: ${mangaName} (ID: ${mangaId}), 章节: ${chapter}`);

        // 创建漫画目录
        const mangaDir = path.join(this.outputDir, this.sanitizeFileName(mangaName));
        await fs.ensureDir(mangaDir);

        // 只在第一章获取漫画简介信息
        if (!skipMangaInfo) {
            console.log(`📋 获取漫画简介信息...`);
            const mangaInfo = await this.getMangaInfo(mangaId, mangaDir);
            if (mangaInfo) {
                const infoPath = path.join(mangaDir, 'manga-info.json');
                await fs.writeFile(infoPath, JSON.stringify(mangaInfo, null, 2), 'utf8');
                console.log(`✅ 漫画简介已保存: ${infoPath}`);
            }
        }

        // 导航到章节页面
        const navigationResult = await this.navigateToChapter(mangaId, chapter);
        if (!navigationResult.success) {
            console.log(`📄 章节 ${chapter} 不存在或无法访问`);
            return false;
        }

        // 获取章节标题
        const chapterTitle = navigationResult.title;
        console.log(`📝 章节标题: ${chapterTitle || '未获取到标题'}`);

        // 创建章节目录
        const chapterDirName = chapterTitle ?
            `第${chapter}章-${this.sanitizeFileName(chapterTitle)}` :
            `第${chapter}章`;

        const chapterDir = path.join(mangaDir, chapterDirName);
        await fs.ensureDir(chapterDir);

        console.log(`📁 章节目录: ${chapterDirName}`);

        // 检查章节是否已完成
        if (await this.isChapterComplete(chapterDir)) {
            console.log(`✅ 章节已完整下载，跳过重复下载`);
            return true;
        }

        // 核心下载流程：滚动页面 -> 等待blob加载 -> 下载图片
        console.log(`🆕 开始下载章节`);
        return await this.downloadChapterImages(chapterDir);
    }

    /**
     * 导航到指定章节
     */
    async navigateToChapter(mangaId, chapter) {
        console.log(`🧭 导航到章节: ${chapter}`);

        const chapterUrl = `https://www.colamanga.com/manga-${mangaId}/1/${chapter}.html`;
        console.log(`🔗 访问章节 URL: ${chapterUrl}`);

        try {
            const response = await this.page.goto(chapterUrl, {
                waitUntil: 'domcontentloaded',
                timeout: 60000
            });

            // 检查响应状态
            if (response.status() === 404) {
                console.log(`❌ 章节 ${chapter} 返回 404，不存在`);
                return { success: false, error: 'Chapter not found' };
            }

            if (response.status() >= 400) {
                console.log(`❌ 章节 ${chapter} 返回状态码: ${response.status()}`);
                return { success: false, error: `HTTP ${response.status()}` };
            }

            // 等待页面基本内容加载
            await this.page.waitForLoadState('domcontentloaded');

            // 验证页面是否包含漫画内容
            const hasContent = await this.verifyChapterContent();
            if (!hasContent) {
                console.log(`❌ 章节 ${chapter} 页面无有效内容`);
                return { success: false, error: 'No valid content' };
            }

            // 获取章节标题
            const title = await this.getChapterTitle();

            console.log(`✅ 成功导航到章节 ${chapter}`);
            return {
                success: true,
                title: title,
                url: chapterUrl
            };

        } catch (error) {
            console.log(`❌ 导航到章节 ${chapter} 失败: ${error.message}`);

            // 特殊处理常见错误
            if (error.message.includes('404') ||
                error.message.includes('net::ERR_HTTP_RESPONSE_CODE_FAILURE')) {
                return { success: false, error: 'Chapter not found' };
            }

            if (error.message.includes('timeout')) {
                return { success: false, error: 'Navigation timeout' };
            }

            throw error; // 重新抛出未知错误
        }
    }

    /**
     * 验证章节页面是否包含有效的漫画内容
     */
    async verifyChapterContent() {
        try {
            // 等待漫画内容容器出现
            await this.page.waitForSelector('.mh_comicpic', { timeout: 10000 });

            // 检查是否有实际的图片内容
            const contentCheck = await this.page.evaluate(() => {
                const comicPics = document.querySelectorAll('.mh_comicpic');
                return comicPics.length > 0;
            });

            return contentCheck;
        } catch (error) {
            console.log(`⚠️ 验证章节内容失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 获取章节标题
     */
    async getChapterTitle() {
        try {
            return await this.page.evaluate(() => {
                const titleElement = document.querySelector('.mh_readtitle');
                console.log('🔍 查找标题元素:', titleElement ? '找到' : '未找到');

                if (titleElement) {
                    let title = titleElement.textContent.trim();
                    console.log('📝 原始标题:', title);

                    // 清理标题，移除导航文本
                    title = title.replace(/返回目录/g, '');
                    title = title.replace(/返回首页/g, '');
                    title = title.replace(/上一章/g, '');
                    title = title.replace(/下一章/g, '');
                    title = title.replace(/\s+/g, ' '); // 合并多个空格
                    title = title.trim();

                    console.log('🧹 清理后标题:', title);
                    return title || null;
                }
                return null;
            });
        } catch (error) {
            console.log('⚠️ 无法获取章节标题:', error.message);
            return null;
        }
    }

    /**
     * 检查章节是否已完成下载
     */
    async isChapterComplete(chapterDir) {
        try {
            // 检查目录是否存在
            if (!(await fs.pathExists(chapterDir))) {
                return false;
            }

            // 检查是否有图片文件
            const files = await fs.readdir(chapterDir);
            const imageFiles = files.filter(file =>
                /\.(png|jpg|jpeg|webp)$/i.test(file)
            );

            // 如果有10张以上图片，认为章节基本完成
            return imageFiles.length >= 10;
        } catch (error) {
            console.log(`⚠️ 检查章节完整性失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 下载章节图片 - 核心流程，支持重试
     */
    async downloadChapterImages(chapterDir, maxRetries = 2) {
        for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
            try {
                if (attempt > 1) {
                    console.log(`🔄 第 ${attempt - 1} 次重试下载章节图片...`);
                }

                console.log(`🔄 开始章节图片下载流程 (尝试 ${attempt}/${maxRetries + 1})`);

                // 1. 等待页面内容加载
                await this.page.waitForSelector('.mh_comicpic', { timeout: 15000 });

                // 2. 滚动页面，触发所有懒加载
                console.log(`📜 开始滚动页面，触发懒加载...`);
                await this.scrollPageToLoadImages();

                // 3. 等待所有blob图片加载完成（内置重试机制）
                console.log(`⏳ 等待blob图片加载完成...`);
                const loadResult = await this.waitForBlobImagesLoaded(60000, 2); // 60秒超时，2次重试

                if (!loadResult.success) {
                    console.log(`❌ blob图片加载失败或不完整`);
                    if (attempt <= maxRetries) {
                        console.log(`🔄 准备重试整个下载流程...`);
                        await new Promise(resolve => setTimeout(resolve, 3000)); // 等待3秒后重试
                        continue;
                    } else {
                        return false;
                    }
                }

                console.log(`✅ 检测到 ${loadResult.imageCount} 张blob图片`);

                // 4. 下载所有blob图片
                console.log(`💾 开始下载blob图片...`);
                const downloadedCount = await this.downloadBlobImages(chapterDir);

                if (downloadedCount > 0) {
                    console.log(`✅ 章节下载完成，共 ${downloadedCount} 张图片`);
                    return true;
                } else {
                    console.log(`⚠️ 未下载到任何图片`);
                    if (attempt <= maxRetries) {
                        console.log(`🔄 准备重试整个下载流程...`);
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        continue;
                    } else {
                        return false;
                    }
                }

            } catch (error) {
                console.error(`❌ 下载章节图片失败 (尝试 ${attempt}): ${error.message}`);
                if (attempt <= maxRetries) {
                    console.log(`🔄 准备重试整个下载流程...`);
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    continue;
                } else {
                    return false;
                }
            }
        }

        return false;
    }

    /**
     * 滚动页面以触发所有懒加载，优化版本
     */
    async scrollPageToLoadImages() {
        console.log(`📜 开始渐进式滚动...`);

        let lastElementCount = 0;
        let stableCount = 0;
        let scrollAttempts = 0;
        const maxScrollAttempts = 80; // 减少最大滚动次数
        const stableThreshold = 3; // 减少稳定阈值

        while (scrollAttempts < maxScrollAttempts) {
            // 滚动页面
            const scrollInfo = await this.page.evaluate(() => {
                const currentScroll = window.scrollY;
                const pageHeight = document.body.scrollHeight;
                const windowHeight = window.innerHeight;
                const isAtBottom = currentScroll + windowHeight >= pageHeight - 100;

                // 渐进式滚动，步长稍大一些
                if (!isAtBottom) {
                    window.scrollBy({
                        top: 1200, // 增加滚动步长
                        behavior: 'smooth'
                    });
                }

                return {
                    currentScroll,
                    pageHeight,
                    windowHeight,
                    isAtBottom,
                    newScroll: window.scrollY
                };
            });

            console.log(`📊 滚动第${scrollAttempts + 1}次: ${scrollInfo.currentScroll} → ${scrollInfo.newScroll}`);

            // 等待滚动完成，时间稍长一些让图片有时间加载
            await new Promise(resolve => setTimeout(resolve, 1500));

            // 检查当前元素数量
            const currentElementCount = await this.page.evaluate(() => {
                const comicPics = document.querySelectorAll('.mh_comicpic');
                const validElements = Array.from(comicPics).filter(pic => {
                    const pValue = pic.getAttribute('p');
                    const errorElement = pic.querySelector('.mh_loaderr');

                    if (errorElement) {
                        const errorStyle = window.getComputedStyle(errorElement);
                        if (errorStyle.display !== 'none') return false;
                    }

                    return pValue !== null;
                });
                return validElements.length;
            });

            console.log(`📊 发现 ${currentElementCount} 个有效元素`);

            // 检查元素数量是否稳定
            if (currentElementCount > lastElementCount) {
                const newElements = currentElementCount - lastElementCount;
                console.log(`📈 发现 ${newElements} 个新元素`);
                stableCount = 0;
                lastElementCount = currentElementCount;

                // 发现新元素时，额外等待一下让图片加载
                await new Promise(resolve => setTimeout(resolve, 2000));
            } else {
                stableCount++;
                console.log(`⏳ 元素数量稳定 ${stableCount}/${stableThreshold} 次`);

                // 如果到达底部且元素数量稳定，认为滚动完成
                if (stableCount >= stableThreshold && scrollInfo.isAtBottom) {
                    console.log(`✅ 页面滚动完成，共发现 ${currentElementCount} 个有效元素`);
                    break;
                }

                // 如果元素数量稳定次数达到阈值，也认为滚动完成
                if (stableCount >= stableThreshold) {
                    console.log(`✅ 元素数量稳定达到阈值，滚动完成`);
                    break;
                }
            }

            scrollAttempts++;
        }

        if (scrollAttempts >= maxScrollAttempts) {
            console.log(`⚠️ 达到最大滚动次数，停止滚动`);
        }

        // 最后确保滚动到页面最底部，并给足够时间加载
        await this.page.evaluate(() => {
            window.scrollTo(0, document.body.scrollHeight);
        });
        await new Promise(resolve => setTimeout(resolve, 3000)); // 增加等待时间

        console.log(`✅ 页面滚动完成`);
    }

    /**
     * 等待所有blob图片加载完成，支持刷新重试
     */
    async waitForBlobImagesLoaded(maxWaitTime = 60000, maxRetries = 2) {
        console.log(`⏳ 等待blob图片加载，最大等待时间: ${maxWaitTime / 1000}秒，最大重试次数: ${maxRetries}`);

        for (let retry = 0; retry <= maxRetries; retry++) {
            if (retry > 0) {
                console.log(`🔄 第 ${retry} 次重试，刷新页面重新加载...`);
                await this.page.reload({ waitUntil: 'domcontentloaded' });

                // 重新滚动页面
                console.log(`📜 重新滚动页面...`);
                await this.scrollPageToLoadImages();
            }

            const startTime = Date.now();
            let lastBlobCount = 0;
            let stableCount = 0;
            const stableThreshold = 5;
            let hasFailedImages = false;

            while (Date.now() - startTime < maxWaitTime) {
                // 检查当前blob图片数量
                const blobResult = await this.page.evaluate(() => {
                    const comicPics = document.querySelectorAll('.mh_comicpic');
                    let validImages = 0;
                    let blobImages = 0;
                    let failedImages = 0;

                    for (const pic of comicPics) {
                        const pValue = pic.getAttribute('p');
                        const img = pic.querySelector('img');
                        const errorElement = pic.querySelector('.mh_loaderr');

                        // 检查是否有错误元素显示
                        if (errorElement) {
                            const errorStyle = window.getComputedStyle(errorElement);
                            if (errorStyle.display !== 'none') {
                                failedImages++;
                                continue;
                            }
                        }

                        if (pValue && img) {
                            validImages++;
                            if (img.src && img.src.startsWith('blob:')) {
                                blobImages++;
                            }
                        }
                    }

                    return {
                        validImages,
                        blobImages,
                        failedImages,
                        loadingRate: validImages > 0 ? (blobImages / validImages * 100) : 0
                    };
                });

                console.log(`📊 blob加载进度: ${blobResult.blobImages}/${blobResult.validImages} (${blobResult.loadingRate.toFixed(1)}%)`);

                // 如果有加载失败的图片，记录但不立即返回失败
                if (blobResult.failedImages > 0) {
                    console.log(`⚠️ 检测到 ${blobResult.failedImages} 张图片加载失败`);
                    hasFailedImages = true;
                    break; // 跳出当前等待循环，进入重试
                }

                // 检查blob数量是否稳定
                if (blobResult.blobImages === lastBlobCount) {
                    stableCount++;
                } else {
                    stableCount = 0;
                    lastBlobCount = blobResult.blobImages;
                }

                // 如果所有图片都加载为blob，或者数量稳定且加载率较高
                if (blobResult.loadingRate >= 95 && stableCount >= stableThreshold) {
                    console.log(`✅ blob图片加载完成: ${blobResult.blobImages}张`);
                    return {
                        success: true,
                        imageCount: blobResult.blobImages,
                        validImages: blobResult.validImages,
                        loadingRate: blobResult.loadingRate
                    };
                }

                // 等待一段时间后重新检查
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            // 如果没有失败图片但超时了，也可以尝试重试
            if (!hasFailedImages) {
                console.log(`⚠️ 等待blob加载超时，但无失败图片`);
                if (retry === maxRetries) {
                    // 最后一次重试也超时，返回当前状态
                    const finalResult = await this.page.evaluate(() => {
                        const comicPics = document.querySelectorAll('.mh_comicpic');
                        let validImages = 0;
                        let blobImages = 0;

                        for (const pic of comicPics) {
                            const pValue = pic.getAttribute('p');
                            const img = pic.querySelector('img');
                            const errorElement = pic.querySelector('.mh_loaderr');

                            if (errorElement) {
                                const errorStyle = window.getComputedStyle(errorElement);
                                if (errorStyle.display !== 'none') continue;
                            }

                            if (pValue && img) {
                                validImages++;
                                if (img.src && img.src.startsWith('blob:')) {
                                    blobImages++;
                                }
                            }
                        }

                        return { validImages, blobImages };
                    });

                    if (finalResult.blobImages > 0) {
                        console.log(`⚠️ 超时但有部分图片加载成功，继续下载: ${finalResult.blobImages}张`);
                        return {
                            success: true,
                            imageCount: finalResult.blobImages,
                            validImages: finalResult.validImages,
                            loadingRate: (finalResult.blobImages / finalResult.validImages * 100)
                        };
                    }
                }
            }
        }

        console.log(`❌ 所有重试都失败了`);
        return { success: false, imageCount: 0 };
    }

    /**
     * 下载所有blob图片
     */
    async downloadBlobImages(chapterDir) {
        console.log(`💾 开始下载blob图片...`);

        // 获取所有blob图片信息
        const blobImages = await this.page.evaluate(() => {
            const comicPics = document.querySelectorAll('.mh_comicpic');
            const images = [];

            for (const pic of comicPics) {
                const pValue = pic.getAttribute('p');
                const img = pic.querySelector('img');

                if (pValue && img && img.src && img.src.startsWith('blob:')) {
                    const order = parseInt(pValue);
                    if (order > 0) {
                        images.push({
                            blobUrl: img.src,
                            order: order
                        });
                    }
                }
            }

            return images.sort((a, b) => a.order - b.order);
        });

        console.log(`🔍 找到 ${blobImages.length} 张blob图片`);

        if (blobImages.length === 0) {
            console.log(`⚠️ 未找到任何blob图片`);
            return 0;
        }

        // 下载图片
        let downloadedCount = 0;
        let skippedCount = 0;
        let failedCount = 0;

        for (const imageInfo of blobImages) {
            try {
                // 生成文件名
                const fileName = `${imageInfo.order}.png`;
                const filePath = path.join(chapterDir, fileName);

                // 检查文件是否已存在
                if (await fs.pathExists(filePath)) {
                    console.log(`⏭️ 文件已存在，跳过: ${fileName}`);
                    skippedCount++;
                    continue;
                }

                // 使用元素截图方式下载
                const imgSelector = `.mh_comicpic[p="${imageInfo.order}"] img`;
                const imgElement = await this.page.$(imgSelector);

                if (imgElement) {
                    console.log(`📸 截图下载: ${fileName}`);
                    const buffer = await imgElement.screenshot({
                        type: 'png',
                        omitBackground: false
                    });

                    await fs.writeFile(filePath, buffer);
                    console.log(`💾 保存成功: ${fileName} (${(buffer.length / 1024).toFixed(1)} KB)`);
                    downloadedCount++;
                } else {
                    console.error(`❌ 未找到图片元素: p=${imageInfo.order}`);
                    failedCount++;
                }

            } catch (error) {
                console.error(`❌ 下载图片失败 (order=${imageInfo.order}): ${error.message}`);
                failedCount++;
            }
        }

        console.log(`✅ 图片下载完成统计:`);
        console.log(`   - 成功下载: ${downloadedCount} 张`);
        console.log(`   - 跳过已存在: ${skippedCount} 张`);
        console.log(`   - 下载失败: ${failedCount} 张`);
        console.log(`   - 总计处理: ${blobImages.length} 张`);

        return downloadedCount;
    }

    // ==================== 漫画信息获取 ====================

    /**
     * 获取漫画简介信息
     */
    async getMangaInfo(mangaId, mangaDir) {
        try {
            const mangaUrl = `https://www.colamanga.com/manga-${mangaId}/`;
            console.log(`🔗 访问漫画详情页: ${mangaUrl}`);

            // 导航到漫画详情页
            await this.page.goto(mangaUrl, {
                waitUntil: 'domcontentloaded',
                timeout: 30000
            });

            // 等待页面加载
            await this.page.waitForSelector('.fed-part-layout.fed-part-rows.fed-back-whits', { timeout: 10000 });

            // 提取漫画信息
            const mangaInfo = await this.page.evaluate(() => {
                const info = {};

                // 查找信息容器
                const infoContainer = document.querySelector('.fed-part-layout.fed-part-rows.fed-back-whits');
                if (!infoContainer) {
                    console.log('❌ 未找到信息容器');
                    return null;
                }

                // 提取基本信息
                const ul = infoContainer.querySelector('.fed-part-rows ul');
                if (ul) {
                    const listItems = ul.querySelectorAll('li');
                    listItems.forEach(li => {
                        const keyElement = li.querySelector('.fed-text-muted');
                        if (keyElement) {
                            const key = keyElement.textContent.trim().replace('：', '').replace(':', '');

                            // 获取除了 key 元素之外的所有文本内容
                            const clonedLi = li.cloneNode(true);
                            const keyElementClone = clonedLi.querySelector('.fed-text-muted');
                            if (keyElementClone) {
                                keyElementClone.remove();
                            }
                            const value = clonedLi.textContent.trim();

                            if (key && value) {
                                info[key] = value;
                                console.log(`📋 提取信息: ${key} = ${value}`);
                            }
                        }
                    });
                }

                // 提取封面图片URL
                let coverUrl = null;

                // 方式1: 从 data-original 属性获取
                const coverElement = infoContainer.querySelector('a[data-original]');
                if (coverElement) {
                    coverUrl = coverElement.getAttribute('data-original');
                    if (coverUrl) {
                        console.log(`🖼️ 从data-original提取封面URL: ${coverUrl}`);
                    }
                }

                // 方式2: 从 background-image CSS 属性获取
                if (!coverUrl) {
                    const backgroundElements = infoContainer.querySelectorAll('*');
                    for (const element of backgroundElements) {
                        const style = window.getComputedStyle(element);
                        const backgroundImage = style.backgroundImage;

                        if (backgroundImage && backgroundImage !== 'none') {
                            const urlMatch = backgroundImage.match(/url\(["']?([^"')]+)["']?\)/);
                            if (urlMatch && urlMatch[1]) {
                                coverUrl = urlMatch[1];
                                console.log(`🖼️ 从background-image提取封面URL: ${coverUrl}`);
                                break;
                            }
                        }
                    }
                }

                // 方式3: 从 img 标签的 src 属性获取
                if (!coverUrl) {
                    const imgElement = infoContainer.querySelector('img');
                    if (imgElement && imgElement.src) {
                        coverUrl = imgElement.src;
                        console.log(`🖼️ 从img src提取封面URL: ${coverUrl}`);
                    }
                }

                if (coverUrl) {
                    info['封面URL'] = coverUrl;
                }

                console.log('📊 提取的漫画信息:', info);
                return info;
            });

            if (mangaInfo && Object.keys(mangaInfo).length > 0) {
                console.log(`✅ 成功获取漫画信息，包含 ${Object.keys(mangaInfo).length} 个字段`);

                // 下载封面图片
                if (mangaInfo['封面URL']) {
                    const coverPath = await this.downloadCoverImage(mangaInfo['封面URL'], mangaDir);
                    if (coverPath) {
                        mangaInfo['封面'] = coverPath;
                        delete mangaInfo['封面URL']; // 删除URL，只保留本地路径
                    }
                }

                return mangaInfo;
            } else {
                console.log('⚠️ 未获取到有效的漫画信息');
                return null;
            }

        } catch (error) {
            console.log(`❌ 获取漫画信息失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 下载封面图片
     */
    async downloadCoverImage(coverUrl, mangaDir) {
        try {
            console.log(`📥 开始下载封面图片: ${coverUrl}`);

            // 获取文件扩展名
            const urlParts = coverUrl.split('.');
            const extension = urlParts[urlParts.length - 1].split('?')[0] || 'jpg';
            const coverFileName = `cover.${extension}`;
            const coverPath = path.join(mangaDir, coverFileName);

            // 检查文件是否已存在
            if (await fs.pathExists(coverPath)) {
                console.log(`⏭️ 封面已存在，跳过下载: ${coverFileName}`);
                return coverFileName;
            }

            // 使用Playwright的request API下载
            try {
                const response = await this.page.request.get(coverUrl, {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Referer': 'https://www.colamanga.com/',
                        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8'
                    },
                    timeout: 15000
                });

                if (response.ok()) {
                    const buffer = await response.body();
                    await fs.writeFile(coverPath, buffer);

                    console.log(`💾 封面下载成功: ${coverFileName} (${(buffer.length / 1024).toFixed(1)} KB)`);
                    return coverFileName;
                } else {
                    console.log(`⚠️ 封面下载失败: HTTP ${response.status()}`);
                }
            } catch (downloadError) {
                console.log(`⚠️ 封面下载失败: ${downloadError.message}`);
            }

            return null;

        } catch (error) {
            console.log(`❌ 下载封面图片失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 文件名清理工具
     */
    sanitizeFileName(fileName) {
        return fileName.replace(/[<>:"/\\|?*]/g, '_').trim();
    }
}

module.exports = { MangaContentDownloader };
